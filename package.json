{"name": "singpass-demo-react-lambda", "version": "1.0.0", "description": "Singpass demo app with React frontend and AWS Lambda backend", "scripts": {"install:all": "cd react-frontend && npm install && cd ../lambda-backend && npm install", "dev:frontend": "cd react-frontend && npm start", "dev:backend": "cd lambda-backend && npx serverless offline", "build:frontend": "cd react-frontend && npm run build", "deploy:backend": "cd lambda-backend && npx serverless deploy", "deploy": "bash deploy.sh", "deploy:windows": "powershell -ExecutionPolicy Bypass -File deploy.ps1"}, "keywords": ["singpass", "react", "aws", "lambda", "serverless"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "serverless-dotenv-plugin": "^6.0.0"}}