import { Command as $Command } from "@smithy/smithy-client";
import {
  BlobPayloadInputTypes,
  MetadataBearer as __MetadataBearer,
} from "@smithy/types";
import {
  APIGatewayClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../APIGatewayClient";
import { ImportRestApiRequest, RestApi } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export type ImportRestApiCommandInputType = Pick<
  ImportRestApiRequest,
  Exclude<keyof ImportRestApiRequest, "body">
> & {
  body: BlobPayloadInputTypes;
};
export interface ImportRestApiCommandInput
  extends ImportRestApiCommandInputType {}
export interface ImportRestApiCommandOutput extends RestApi, __MetadataBearer {}
declare const ImportRestApiCommand_base: {
  new (
    input: ImportRestApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ImportRestApiCommandInput,
    ImportRestApiCommandOutput,
    APIGatewayClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ImportRestApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ImportRestApiCommandInput,
    ImportRestApiCommandOutput,
    APIGatewayClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ImportRestApiCommand extends ImportRestApiCommand_base {
  protected static __types: {
    api: {
      input: ImportRestApiRequest;
      output: RestApi;
    };
    sdk: {
      input: ImportRestApiCommandInput;
      output: ImportRestApiCommandOutput;
    };
  };
}
