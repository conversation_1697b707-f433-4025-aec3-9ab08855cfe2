{"name": "demo-app", "version": "0.0.1", "scripts": {"start": "set NODE_EXTRA_CA_CERTS=D:\\Techpass-Cloudflare\\Cloudflare_CA.pem && node src/server.mjs"}, "license": "MIT", "dependencies": {"@koa/router": "^13.0.0", "koa": "^2.15.3", "koa-logger": "^3.2.1", "koa-session": "^6.4.0", "koa-static": "^5.0.0", "openid-client": "^5.6.5"}, "devDependencies": {"prettier": "latest"}, "prettier": {"printWidth": 120, "singleQuote": true, "trailingComma": "es5"}}