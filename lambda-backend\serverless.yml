service: singpass-demo-backend

frameworkVersion: "3"

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-southeast-1
  stage: dev

  environment:
    CLIENT_ID: ${env:CLIENT_ID}
    ISSUER_URL: ${env:ISSUER_URL, 'https://stg-id.singpass.gov.sg'}
    REDIRECT_URI: ${env:REDIRECT_URI}
    SCOPES: ${env:SCOPES, 'openid uinfin name'}
    SESSION_TABLE: ${self:service}-${self:provider.stage}-sessions
    FRONTEND_URL: ${env:FRONTEND_URL, 'http://localhost:3000'}
    PRIVATE_SIG_KEY: ${env:PRIVATE_SIG_KEY}
    PRIVATE_ENC_KEY: ${env:PRIVATE_ENC_KEY}
    PUBLIC_SIG_KEY: ${env:PUBLIC_SIG_KEY}
    PUBLIC_ENC_KEY: ${env:PUBLIC_ENC_KEY}

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - Fn::GetAtt: [SessionsTable, Arn]

functions:
  jwks:
    handler: src/handlers/jwks.handler
    events:
      - http:
          path: /.well-known/jwks.json
          method: get
          cors: true

  login:
    handler: src/handlers/login.handler
    events:
      - http:
          path: /api/login
          method: get
          cors: true

  callback:
    handler: src/handlers/callback.handler
    events:
      - http:
          path: /api/callback
          method: get
          cors: true

  user:
    handler: src/handlers/user.handler
    events:
      - http:
          path: /api/user
          method: get
          cors: true

  logout:
    handler: src/handlers/logout.handler
    events:
      - http:
          path: /api/logout
          method: get
          cors: true

resources:
  Resources:
    SessionsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:provider.environment.SESSION_TABLE}
        AttributeDefinitions:
          - AttributeName: sessionId
            AttributeType: S
        KeySchema:
          - AttributeName: sessionId
            KeyType: HASH
        TimeToLiveSpecification:
          AttributeName: ttl
          Enabled: true
        BillingMode: PAY_PER_REQUEST

plugins:
  - serverless-dotenv-plugin
  - serverless-offline
