import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  APIGatewayClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../APIGatewayClient";
import { BasePathMapping, GetBasePathMappingRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetBasePathMappingCommandInput
  extends GetBasePathMappingRequest {}
export interface GetBasePathMappingCommandOutput
  extends BasePathMapping,
    __MetadataBearer {}
declare const GetBasePathMappingCommand_base: {
  new (
    input: GetBasePathMappingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBasePathMappingCommandInput,
    GetBasePathMappingCommandOutput,
    APIGatewayClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetBasePathMappingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetBasePathMappingCommandInput,
    GetBasePathMappingCommandOutput,
    APIGatewayClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetBasePathMappingCommand extends GetBasePathMappingCommand_base {
  protected static __types: {
    api: {
      input: GetBasePathMappingRequest;
      output: BasePathMapping;
    };
    sdk: {
      input: GetBasePathMappingCommandInput;
      output: GetBasePathMappingCommandOutput;
    };
  };
}
