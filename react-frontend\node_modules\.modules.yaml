hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.3':
    '@adobe/css-tools': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/eslint-parser@7.28.0(@babel/core@7.28.0)(eslint@8.57.1)':
    '@babel/eslint-parser': public
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-class-properties': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-proposal-nullish-coalescing-operator@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-nullish-coalescing-operator': private
  '@babel/plugin-proposal-numeric-separator@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-numeric-separator': private
  '@babel/plugin-proposal-optional-chaining@7.21.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-optional-chaining': private
  '@babel/plugin-proposal-private-methods@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-proposal-private-methods': private
  '@babel/plugin-proposal-private-property-in-object@7.21.11(@babel/core@7.28.0)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-flow': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.28.0)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.28.0)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-flow-strip-types@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-flow-strip-types': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-react-constant-elements@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-constant-elements': private
  '@babel/plugin-transform-react-display-name@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-display-name': private
  '@babel/plugin-transform-react-jsx-development@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-development': private
  '@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx': private
  '@babel/plugin-transform-react-pure-annotations@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-pure-annotations': private
  '@babel/plugin-transform-regenerator@7.28.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-runtime@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-runtime': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.0(@babel/core@7.28.0)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.0)':
    '@babel/preset-modules': private
  '@babel/preset-react@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-react': private
  '@babel/preset-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/preset-typescript': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@csstools/normalize.css@12.1.1':
    '@csstools/normalize.css': private
  '@csstools/postcss-cascade-layers@1.1.1(postcss@8.5.6)':
    '@csstools/postcss-cascade-layers': private
  '@csstools/postcss-color-function@1.1.1(postcss@8.5.6)':
    '@csstools/postcss-color-function': private
  '@csstools/postcss-font-format-keywords@1.0.1(postcss@8.5.6)':
    '@csstools/postcss-font-format-keywords': private
  '@csstools/postcss-hwb-function@1.0.2(postcss@8.5.6)':
    '@csstools/postcss-hwb-function': private
  '@csstools/postcss-ic-unit@1.0.1(postcss@8.5.6)':
    '@csstools/postcss-ic-unit': private
  '@csstools/postcss-is-pseudo-class@2.0.7(postcss@8.5.6)':
    '@csstools/postcss-is-pseudo-class': private
  '@csstools/postcss-nested-calc@1.0.0(postcss@8.5.6)':
    '@csstools/postcss-nested-calc': private
  '@csstools/postcss-normalize-display-values@1.0.1(postcss@8.5.6)':
    '@csstools/postcss-normalize-display-values': private
  '@csstools/postcss-oklab-function@1.1.1(postcss@8.5.6)':
    '@csstools/postcss-oklab-function': private
  '@csstools/postcss-progressive-custom-properties@1.3.0(postcss@8.5.6)':
    '@csstools/postcss-progressive-custom-properties': private
  '@csstools/postcss-stepped-value-functions@1.0.1(postcss@8.5.6)':
    '@csstools/postcss-stepped-value-functions': private
  '@csstools/postcss-text-decoration-shorthand@1.0.0(postcss@8.5.6)':
    '@csstools/postcss-text-decoration-shorthand': private
  '@csstools/postcss-trigonometric-functions@1.0.2(postcss@8.5.6)':
    '@csstools/postcss-trigonometric-functions': private
  '@csstools/postcss-unset-value@1.0.2(postcss@8.5.6)':
    '@csstools/postcss-unset-value': private
  '@csstools/selector-specificity@2.2.0(postcss-selector-parser@6.1.2)':
    '@csstools/selector-specificity': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': public
  '@eslint/js@8.57.1':
    '@eslint/js': public
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@27.5.1':
    '@jest/console': private
  '@jest/core@27.5.1':
    '@jest/core': private
  '@jest/diff-sequences@30.0.1':
    '@jest/diff-sequences': private
  '@jest/environment@27.5.1':
    '@jest/environment': private
  '@jest/expect-utils@30.0.5':
    '@jest/expect-utils': private
  '@jest/fake-timers@27.5.1':
    '@jest/fake-timers': private
  '@jest/get-type@30.0.1':
    '@jest/get-type': private
  '@jest/globals@27.5.1':
    '@jest/globals': private
  '@jest/pattern@30.0.1':
    '@jest/pattern': private
  '@jest/reporters@27.5.1':
    '@jest/reporters': private
  '@jest/schemas@28.1.3':
    '@jest/schemas': private
  '@jest/source-map@27.5.1':
    '@jest/source-map': private
  '@jest/test-result@27.5.1':
    '@jest/test-result': private
  '@jest/test-sequencer@27.5.1':
    '@jest/test-sequencer': private
  '@jest/transform@27.5.1':
    '@jest/transform': private
  '@jest/types@27.5.1':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@leichtgewicht/ip-codec@2.0.5':
    '@leichtgewicht/ip-codec': private
  '@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1':
    '@nicolo-ribaudo/eslint-scope-5-internals': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pmmmwh/react-refresh-webpack-plugin@0.5.17(react-refresh@0.11.0)(type-fest@0.21.3)(webpack-dev-server@4.15.2(webpack@5.100.2))(webpack@5.100.2)':
    '@pmmmwh/react-refresh-webpack-plugin': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.28.0)(@types/babel__core@7.20.5)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-node-resolve@11.2.1(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/pluginutils@3.1.0(rollup@2.79.2)':
    '@rollup/pluginutils': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.12.0':
    '@rushstack/eslint-patch': public
  '@sinclair/typebox@0.24.51':
    '@sinclair/typebox': private
  '@sinonjs/commons@1.8.6':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@8.1.0':
    '@sinonjs/fake-timers': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@svgr/babel-plugin-add-jsx-attribute@5.4.0':
    '@svgr/babel-plugin-add-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-attribute@5.4.0':
    '@svgr/babel-plugin-remove-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-empty-expression@5.0.1':
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  '@svgr/babel-plugin-replace-jsx-attribute-value@5.0.1':
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  '@svgr/babel-plugin-svg-dynamic-title@5.4.0':
    '@svgr/babel-plugin-svg-dynamic-title': private
  '@svgr/babel-plugin-svg-em-dimensions@5.4.0':
    '@svgr/babel-plugin-svg-em-dimensions': private
  '@svgr/babel-plugin-transform-react-native-svg@5.4.0':
    '@svgr/babel-plugin-transform-react-native-svg': private
  '@svgr/babel-plugin-transform-svg-component@5.5.0':
    '@svgr/babel-plugin-transform-svg-component': private
  '@svgr/babel-preset@5.5.0':
    '@svgr/babel-preset': private
  '@svgr/core@5.5.0':
    '@svgr/core': private
  '@svgr/hast-util-to-babel-ast@5.5.0':
    '@svgr/hast-util-to-babel-ast': private
  '@svgr/plugin-jsx@5.5.0':
    '@svgr/plugin-jsx': private
  '@svgr/plugin-svgo@5.5.0':
    '@svgr/plugin-svgo': private
  '@svgr/webpack@5.5.0':
    '@svgr/webpack': private
  '@testing-library/dom@8.20.1':
    '@testing-library/dom': private
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/bonjour@3.5.13':
    '@types/bonjour': private
  '@types/connect-history-api-fallback@1.5.4':
    '@types/connect-history-api-fallback': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': public
  '@types/eslint@8.56.12':
    '@types/eslint': public
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.7':
    '@types/express-serve-static-core': private
  '@types/express@4.17.23':
    '@types/express': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/html-minifier-terser@6.1.0':
    '@types/html-minifier-terser': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/http-proxy@1.17.16':
    '@types/http-proxy': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@30.0.0':
    '@types/jest': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/node-forge@1.3.13':
    '@types/node-forge': private
  '@types/node@24.1.0':
    '@types/node': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prettier@2.7.3':
    '@types/prettier': public
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/q@1.5.8':
    '@types/q': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    '@types/react-dom': private
  '@types/react@18.3.23':
    '@types/react': private
  '@types/resolve@1.17.1':
    '@types/resolve': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-index@1.9.4':
    '@types/serve-index': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/sockjs@0.3.36':
    '@types/sockjs': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/testing-library__jest-dom@5.14.9':
    '@types/testing-library__jest-dom': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@16.0.9':
    '@types/yargs': private
  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/experimental-utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    '@typescript-eslint/experimental-utils': public
  '@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@5.62.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@5.62.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@5.62.0(eslint@8.57.1)(typescript@4.9.5)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@5.62.0':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abab@2.0.6:
    abab: private
  accepts@1.3.8:
    accepts: private
  acorn-globals@6.0.0:
    acorn-globals: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn-walk@7.2.0:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  address@1.2.2:
    address: private
  adjust-sourcemap-loader@4.0.0:
    adjust-sourcemap-loader: private
  agent-base@6.0.2:
    agent-base: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-html-community@0.0.8:
    ansi-html-community: private
  ansi-html@0.0.9:
    ansi-html: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.9:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.reduce@1.0.8:
    array.prototype.reduce: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  asap@2.0.6:
    asap: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  autoprefixer@10.4.21(postcss@8.5.6):
    autoprefixer: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  babel-jest@27.5.1(@babel/core@7.28.0):
    babel-jest: private
  babel-loader@8.4.1(@babel/core@7.28.0)(webpack@5.100.2):
    babel-loader: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@27.5.1:
    babel-plugin-jest-hoist: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  babel-plugin-named-asset-import@0.3.8(@babel/core@7.28.0):
    babel-plugin-named-asset-import: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    babel-plugin-polyfill-regenerator: private
  babel-plugin-transform-react-remove-prop-types@0.4.24:
    babel-plugin-transform-react-remove-prop-types: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  babel-preset-jest@27.5.1(@babel/core@7.28.0):
    babel-preset-jest: private
  babel-preset-react-app@10.1.0:
    babel-preset-react-app: private
  balanced-match@1.0.2:
    balanced-match: private
  batch@0.6.1:
    batch: private
  bfj@7.1.0:
    bfj: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bluebird@3.7.2:
    bluebird: private
  body-parser@1.20.3:
    body-parser: private
  bonjour-service@1.3.0:
    bonjour-service: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browser-process-hrtime@1.0.0:
    browser-process-hrtime: private
  browserslist@4.25.1:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-from@1.1.2:
    buffer-from: private
  builtin-modules@3.3.0:
    builtin-modules: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-api@3.0.0:
    caniuse-api: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  case-sensitive-paths-webpack-plugin@2.4.0:
    case-sensitive-paths-webpack-plugin: private
  chalk@3.0.0:
    chalk: private
  char-regex@2.0.2:
    char-regex: private
  check-types@11.2.3:
    check-types: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  clean-css@5.3.3:
    clean-css: private
  cliui@7.0.4:
    cliui: private
  co@4.6.0:
    co: private
  coa@2.0.2:
    coa: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  colord@2.9.3:
    colord: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@8.3.0:
    commander: private
  common-tags@1.8.2:
    common-tags: private
  commondir@1.0.1:
    commondir: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.1:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  confusing-browser-globals@1.0.11:
    confusing-browser-globals: private
  connect-history-api-fallback@2.0.0:
    connect-history-api-fallback: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-js-compat@3.44.0:
    core-js-compat: private
  core-js-pure@3.44.0:
    core-js-pure: private
  core-js@3.44.0:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-blank-pseudo@3.0.3(postcss@8.5.6):
    css-blank-pseudo: private
  css-declaration-sorter@6.4.1(postcss@8.5.6):
    css-declaration-sorter: private
  css-has-pseudo@3.0.4(postcss@8.5.6):
    css-has-pseudo: private
  css-loader@6.11.0(webpack@5.100.2):
    css-loader: private
  css-minimizer-webpack-plugin@3.4.1(webpack@5.100.2):
    css-minimizer-webpack-plugin: private
  css-prefers-color-scheme@6.0.3(postcss@8.5.6):
    css-prefers-color-scheme: private
  css-select-base-adapter@0.1.1:
    css-select-base-adapter: private
  css-select@4.3.0:
    css-select: private
  css-tree@1.0.0-alpha.37:
    css-tree: private
  css-what@3.4.2:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  cssdb@7.11.2:
    cssdb: private
  cssesc@3.0.0:
    cssesc: private
  cssnano-preset-default@5.2.14(postcss@8.5.6):
    cssnano-preset-default: private
  cssnano-utils@3.1.0(postcss@8.5.6):
    cssnano-utils: private
  cssnano@5.1.15(postcss@8.5.6):
    cssnano: private
  csso@4.2.0:
    csso: private
  cssom@0.4.4:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-urls@2.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decimal.js@10.6.0:
    decimal.js: private
  dedent@0.7.0:
    dedent: private
  deep-equal@2.2.3:
    deep-equal: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-gateway@6.0.3:
    default-gateway: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-newline@3.1.0:
    detect-newline: private
  detect-node@2.1.0:
    detect-node: private
  detect-port-alt@1.1.6:
    detect-port-alt: private
  didyoumean@1.2.2:
    didyoumean: private
  diff-sequences@27.5.1:
    diff-sequences: private
  dir-glob@3.0.1:
    dir-glob: private
  dlv@1.1.3:
    dlv: private
  dns-packet@5.6.1:
    dns-packet: private
  doctrine@3.0.0:
    doctrine: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dom-converter@0.2.0:
    dom-converter: private
  dom-serializer@0.2.2:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domexception@2.0.1:
    domexception: private
  domhandler@4.3.1:
    domhandler: private
  domutils@1.7.0:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dotenv-expand@5.1.0:
    dotenv-expand: private
  dotenv@10.0.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.190:
    electron-to-chromium: private
  emittery@0.8.1:
    emittery: private
  emoji-regex@9.2.2:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@2.2.0:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-abstract@1.24.0:
    es-abstract: private
  es-array-method-boxes-properly@1.0.0:
    es-array-method-boxes-properly: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-get-iterator@1.1.3:
    es-get-iterator: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@1.14.3:
    escodegen: private
  eslint-config-react-app@7.0.1(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1)(jest@27.5.1)(typescript@4.9.5):
    eslint-config-react-app: public
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-module-utils@2.12.1(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    eslint-module-utils: public
  eslint-plugin-flowtype@8.0.3(@babel/plugin-syntax-flow@7.27.1(@babel/core@7.28.0))(@babel/plugin-transform-react-jsx@7.27.1(@babel/core@7.28.0))(eslint@8.57.1):
    eslint-plugin-flowtype: public
  eslint-plugin-import@2.32.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1):
    eslint-plugin-import: public
  eslint-plugin-jest@25.7.0(@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(typescript@4.9.5))(eslint@8.57.1)(jest@27.5.1)(typescript@4.9.5):
    eslint-plugin-jest: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  eslint-plugin-testing-library@5.11.1(eslint@8.57.1)(typescript@4.9.5):
    eslint-plugin-testing-library: public
  eslint-scope@7.2.2:
    eslint-scope: public
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: public
  eslint-webpack-plugin@3.2.0(eslint@8.57.1)(webpack@5.100.2):
    eslint-webpack-plugin: public
  eslint@8.57.1:
    eslint: public
  espree@9.6.1:
    espree: private
  esprima@1.2.2:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@1.0.1:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@30.0.5:
    expect: private
  express@4.21.2:
    express: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fb-watchman@2.0.2:
    fb-watchman: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-loader@6.2.0(webpack@5.100.2):
    file-loader: private
  filelist@1.0.4:
    filelist: private
  filesize@8.0.7:
    filesize: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-cache-dir@3.3.2:
    find-cache-dir: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  fork-ts-checker-webpack-plugin@6.5.3(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.2):
    fork-ts-checker-webpack-plugin: private
  form-data@3.0.4:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fraction.js@4.3.7:
    fraction.js: private
  fresh@0.5.2:
    fresh: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-monkey@1.1.0:
    fs-monkey: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@7.2.3:
    glob: private
  global-modules@2.0.0:
    global-modules: private
  global-prefix@3.0.0:
    global-prefix: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@6.0.0:
    gzip-size: private
  handle-thing@2.0.1:
    handle-thing: private
  harmony-reflect@1.6.2:
    harmony-reflect: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  hoopy@0.1.4:
    hoopy: private
  hpack.js@2.1.6:
    hpack.js: private
  html-encoding-sniffer@2.0.1:
    html-encoding-sniffer: private
  html-entities@2.6.0:
    html-entities: private
  html-escaper@2.0.2:
    html-escaper: private
  html-minifier-terser@6.1.0:
    html-minifier-terser: private
  html-webpack-plugin@5.6.3(webpack@5.100.2):
    html-webpack-plugin: private
  htmlparser2@6.1.0:
    htmlparser2: private
  http-deceiver@1.2.7:
    http-deceiver: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-agent@4.0.1:
    http-proxy-agent: private
  http-proxy-middleware@2.0.9(@types/express@4.17.23):
    http-proxy-middleware: private
  http-proxy@1.18.1:
    http-proxy: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  icss-utils@5.1.0(postcss@8.5.6):
    icss-utils: private
  idb@7.1.1:
    idb: private
  identity-obj-proxy@3.0.0:
    identity-obj-proxy: private
  ignore@5.3.2:
    ignore: private
  immer@9.0.21:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.3:
    inherits: private
  ini@1.3.8:
    ini: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@3.0.0:
    is-plain-obj: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-root@2.1.0:
    is-root: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@27.5.1:
    jest-changed-files: private
  jest-circus@27.5.1:
    jest-circus: private
  jest-cli@27.5.1:
    jest-cli: private
  jest-config@27.5.1:
    jest-config: private
  jest-diff@27.5.1:
    jest-diff: private
  jest-docblock@27.5.1:
    jest-docblock: private
  jest-each@27.5.1:
    jest-each: private
  jest-environment-jsdom@27.5.1:
    jest-environment-jsdom: private
  jest-environment-node@27.5.1:
    jest-environment-node: private
  jest-get-type@27.5.1:
    jest-get-type: private
  jest-haste-map@27.5.1:
    jest-haste-map: private
  jest-jasmine2@27.5.1:
    jest-jasmine2: private
  jest-leak-detector@27.5.1:
    jest-leak-detector: private
  jest-matcher-utils@30.0.5:
    jest-matcher-utils: private
  jest-message-util@27.5.1:
    jest-message-util: private
  jest-mock@30.0.5:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@27.5.1):
    jest-pnp-resolver: private
  jest-regex-util@28.0.2:
    jest-regex-util: private
  jest-resolve-dependencies@27.5.1:
    jest-resolve-dependencies: private
  jest-resolve@27.5.1:
    jest-resolve: private
  jest-runner@27.5.1:
    jest-runner: private
  jest-runtime@27.5.1:
    jest-runtime: private
  jest-serializer@27.5.1:
    jest-serializer: private
  jest-snapshot@27.5.1:
    jest-snapshot: private
  jest-util@27.5.1:
    jest-util: private
  jest-validate@27.5.1:
    jest-validate: private
  jest-watch-typeahead@1.1.0(jest@27.5.1):
    jest-watch-typeahead: private
  jest-watcher@28.1.3:
    jest-watcher: private
  jest-worker@27.5.1:
    jest-worker: private
  jest@27.5.1:
    jest: private
  jiti@1.21.7:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsdom@16.7.0:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonpath@1.1.1:
    jsonpath: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  klona@2.0.6:
    klona: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  launch-editor@2.10.0:
    launch-editor: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  lodash@4.17.21:
    lodash: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case@2.0.2:
    lower-case: private
  lru-cache@5.1.1:
    lru-cache: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.25.9:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdn-data@2.0.4:
    mdn-data: private
  media-typer@0.3.0:
    media-typer: private
  memfs@3.5.3:
    memfs: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  min-indent@1.0.1:
    min-indent: private
  mini-css-extract-plugin@2.9.2(webpack@5.100.2):
    mini-css-extract-plugin: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  multicast-dns@7.2.5:
    multicast-dns: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare-lite@1.4.0:
    natural-compare-lite: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.4:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  no-case@3.0.4:
    no-case: private
  node-forge@1.3.1:
    node-forge: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  normalize-url@6.1.0:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@1.0.2:
    nth-check: private
  nwsapi@2.2.20:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-is@1.1.6:
    object-is: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.getownpropertydescriptors@2.1.8:
    object.getownpropertydescriptors: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  obuf@1.1.2:
    obuf: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.1.0:
    on-headers: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@8.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-retry@4.6.2:
    p-retry: private
  p-try@2.2.0:
    p-try: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse5@6.0.1:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-up@3.1.0:
    pkg-up: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-attribute-case-insensitive@5.0.2(postcss@8.5.6):
    postcss-attribute-case-insensitive: private
  postcss-browser-comments@4.0.0(browserslist@4.25.1)(postcss@8.5.6):
    postcss-browser-comments: private
  postcss-calc@8.2.4(postcss@8.5.6):
    postcss-calc: private
  postcss-clamp@4.1.0(postcss@8.5.6):
    postcss-clamp: private
  postcss-color-functional-notation@4.2.4(postcss@8.5.6):
    postcss-color-functional-notation: private
  postcss-color-hex-alpha@8.0.4(postcss@8.5.6):
    postcss-color-hex-alpha: private
  postcss-color-rebeccapurple@7.1.1(postcss@8.5.6):
    postcss-color-rebeccapurple: private
  postcss-colormin@5.3.1(postcss@8.5.6):
    postcss-colormin: private
  postcss-convert-values@5.1.3(postcss@8.5.6):
    postcss-convert-values: private
  postcss-custom-media@8.0.2(postcss@8.5.6):
    postcss-custom-media: private
  postcss-custom-properties@12.1.11(postcss@8.5.6):
    postcss-custom-properties: private
  postcss-custom-selectors@6.0.3(postcss@8.5.6):
    postcss-custom-selectors: private
  postcss-dir-pseudo-class@6.0.5(postcss@8.5.6):
    postcss-dir-pseudo-class: private
  postcss-discard-comments@5.1.2(postcss@8.5.6):
    postcss-discard-comments: private
  postcss-discard-duplicates@5.1.0(postcss@8.5.6):
    postcss-discard-duplicates: private
  postcss-discard-empty@5.1.1(postcss@8.5.6):
    postcss-discard-empty: private
  postcss-discard-overridden@5.1.0(postcss@8.5.6):
    postcss-discard-overridden: private
  postcss-double-position-gradients@3.1.2(postcss@8.5.6):
    postcss-double-position-gradients: private
  postcss-env-function@4.0.6(postcss@8.5.6):
    postcss-env-function: private
  postcss-flexbugs-fixes@5.0.2(postcss@8.5.6):
    postcss-flexbugs-fixes: private
  postcss-focus-visible@6.0.4(postcss@8.5.6):
    postcss-focus-visible: private
  postcss-focus-within@5.0.4(postcss@8.5.6):
    postcss-focus-within: private
  postcss-font-variant@5.0.0(postcss@8.5.6):
    postcss-font-variant: private
  postcss-gap-properties@3.0.5(postcss@8.5.6):
    postcss-gap-properties: private
  postcss-image-set-function@4.0.7(postcss@8.5.6):
    postcss-image-set-function: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-initial@4.0.1(postcss@8.5.6):
    postcss-initial: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-lab-function@4.2.1(postcss@8.5.6):
    postcss-lab-function: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-loader@6.2.1(postcss@8.5.6)(webpack@5.100.2):
    postcss-loader: private
  postcss-logical@5.0.4(postcss@8.5.6):
    postcss-logical: private
  postcss-media-minmax@5.0.0(postcss@8.5.6):
    postcss-media-minmax: private
  postcss-merge-longhand@5.1.7(postcss@8.5.6):
    postcss-merge-longhand: private
  postcss-merge-rules@5.1.4(postcss@8.5.6):
    postcss-merge-rules: private
  postcss-minify-font-values@5.1.0(postcss@8.5.6):
    postcss-minify-font-values: private
  postcss-minify-gradients@5.1.1(postcss@8.5.6):
    postcss-minify-gradients: private
  postcss-minify-params@5.1.4(postcss@8.5.6):
    postcss-minify-params: private
  postcss-minify-selectors@5.2.1(postcss@8.5.6):
    postcss-minify-selectors: private
  postcss-modules-extract-imports@3.1.0(postcss@8.5.6):
    postcss-modules-extract-imports: private
  postcss-modules-local-by-default@4.2.0(postcss@8.5.6):
    postcss-modules-local-by-default: private
  postcss-modules-scope@3.2.1(postcss@8.5.6):
    postcss-modules-scope: private
  postcss-modules-values@4.0.0(postcss@8.5.6):
    postcss-modules-values: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-nesting@10.2.0(postcss@8.5.6):
    postcss-nesting: private
  postcss-normalize-charset@5.1.0(postcss@8.5.6):
    postcss-normalize-charset: private
  postcss-normalize-display-values@5.1.0(postcss@8.5.6):
    postcss-normalize-display-values: private
  postcss-normalize-positions@5.1.1(postcss@8.5.6):
    postcss-normalize-positions: private
  postcss-normalize-repeat-style@5.1.1(postcss@8.5.6):
    postcss-normalize-repeat-style: private
  postcss-normalize-string@5.1.0(postcss@8.5.6):
    postcss-normalize-string: private
  postcss-normalize-timing-functions@5.1.0(postcss@8.5.6):
    postcss-normalize-timing-functions: private
  postcss-normalize-unicode@5.1.1(postcss@8.5.6):
    postcss-normalize-unicode: private
  postcss-normalize-url@5.1.0(postcss@8.5.6):
    postcss-normalize-url: private
  postcss-normalize-whitespace@5.1.1(postcss@8.5.6):
    postcss-normalize-whitespace: private
  postcss-normalize@10.0.1(browserslist@4.25.1)(postcss@8.5.6):
    postcss-normalize: private
  postcss-opacity-percentage@1.1.3(postcss@8.5.6):
    postcss-opacity-percentage: private
  postcss-ordered-values@5.1.3(postcss@8.5.6):
    postcss-ordered-values: private
  postcss-overflow-shorthand@3.0.4(postcss@8.5.6):
    postcss-overflow-shorthand: private
  postcss-page-break@3.0.4(postcss@8.5.6):
    postcss-page-break: private
  postcss-place@7.0.5(postcss@8.5.6):
    postcss-place: private
  postcss-preset-env@7.8.3(postcss@8.5.6):
    postcss-preset-env: private
  postcss-pseudo-class-any-link@7.1.6(postcss@8.5.6):
    postcss-pseudo-class-any-link: private
  postcss-reduce-initial@5.1.2(postcss@8.5.6):
    postcss-reduce-initial: private
  postcss-reduce-transforms@5.1.0(postcss@8.5.6):
    postcss-reduce-transforms: private
  postcss-replace-overflow-wrap@4.0.0(postcss@8.5.6):
    postcss-replace-overflow-wrap: private
  postcss-selector-not@6.0.1(postcss@8.5.6):
    postcss-selector-not: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss-svgo@5.1.0(postcss@8.5.6):
    postcss-svgo: private
  postcss-unique-selectors@5.1.1(postcss@8.5.6):
    postcss-unique-selectors: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@5.6.0:
    pretty-bytes: private
  pretty-error@4.0.0:
    pretty-error: private
  pretty-format@27.5.1:
    pretty-format: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  promise@8.3.0:
    promise: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  psl@1.15.0:
    psl: private
  punycode@2.3.1:
    punycode: private
  q@1.5.1:
    q: private
  qs@6.13.0:
    qs: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  raf@3.4.1:
    raf: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  react-app-polyfill@3.0.0:
    react-app-polyfill: private
  react-dev-utils@12.0.1(eslint@8.57.1)(typescript@4.9.5)(webpack@5.100.2):
    react-dev-utils: private
  react-error-overlay@6.1.0:
    react-error-overlay: private
  react-is@18.3.1:
    react-is: private
  react-refresh@0.11.0:
    react-refresh: private
  react-router@6.30.1(react@18.3.1):
    react-router: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  recursive-readdir@2.2.3:
    recursive-readdir: private
  redent@3.0.0:
    redent: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  regex-parser@2.3.1:
    regex-parser: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  renderkid@3.0.0:
    renderkid: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-url-loader@4.0.0:
    resolve-url-loader: private
  resolve.exports@1.1.1:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup-plugin-terser@7.0.2(rollup@2.79.2):
    rollup-plugin-terser: private
  rollup@2.79.2:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize.css@13.0.0:
    sanitize.css: private
  sass-loader@12.6.0(webpack@5.100.2):
    sass-loader: private
  sax@1.2.4:
    sax: private
  saxes@5.0.1:
    saxes: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  select-hose@2.0.0:
    select-hose: private
  selfsigned@2.4.1:
    selfsigned: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-index@1.9.1:
    serve-index: private
  serve-static@1.16.2:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.3:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  sockjs@0.3.24:
    sockjs: private
  source-list-map@2.0.1:
    source-list-map: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-loader@3.0.2(webpack@5.100.2):
    source-map-loader: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  spdy-transport@3.0.0:
    spdy-transport: private
  spdy@4.0.2:
    spdy: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stable@0.1.8:
    stable: private
  stack-utils@2.0.6:
    stack-utils: private
  stackframe@1.3.4:
    stackframe: private
  static-eval@2.0.2:
    static-eval: private
  statuses@2.0.1:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  string-length@5.0.1:
    string-length: private
  string-natural-compare@3.0.1:
    string-natural-compare: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-loader@3.3.4(webpack@5.100.2):
    style-loader: private
  stylehacks@5.1.1(postcss@8.5.6):
    stylehacks: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-parser@2.0.4:
    svg-parser: private
  svgo@1.3.2:
    svgo: private
  symbol-tree@3.2.4:
    symbol-tree: private
  tailwindcss@3.4.17:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terminal-link@2.1.1:
    terminal-link: private
  terser-webpack-plugin@5.3.14(webpack@5.100.2):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  test-exclude@6.0.0:
    test-exclude: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throat@6.0.2:
    throat: private
  thunky@1.1.0:
    thunky: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@1.0.1:
    tr46: private
  tryer@1.0.1:
    tryer: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsutils@3.21.0(typescript@4.9.5):
    tsutils: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.21.3:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typescript@4.9.5:
    typescript: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  underscore@1.12.1:
    underscore: private
  undici-types@7.8.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unique-string@2.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unquote@1.1.1:
    unquote: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util.promisify@1.0.1:
    util.promisify: private
  utila@0.4.0:
    utila: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  v8-to-istanbul@8.1.1:
    v8-to-istanbul: private
  vary@1.1.2:
    vary: private
  w3c-hr-time@1.0.2:
    w3c-hr-time: private
  w3c-xmlserializer@2.0.0:
    w3c-xmlserializer: private
  walker@1.0.8:
    walker: private
  watchpack@2.4.4:
    watchpack: private
  wbuf@1.7.3:
    wbuf: private
  webidl-conversions@4.0.2:
    webidl-conversions: private
  webpack-dev-middleware@5.3.4(webpack@5.100.2):
    webpack-dev-middleware: private
  webpack-dev-server@4.15.2(webpack@5.100.2):
    webpack-dev-server: private
  webpack-manifest-plugin@4.1.1(webpack@5.100.2):
    webpack-manifest-plugin: private
  webpack-sources@2.3.1:
    webpack-sources: private
  webpack@5.100.2:
    webpack: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whatwg-encoding@1.0.5:
    whatwg-encoding: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-mimetype@2.3.0:
    whatwg-mimetype: private
  whatwg-url@7.1.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  workbox-background-sync@6.6.0:
    workbox-background-sync: private
  workbox-broadcast-update@6.6.0:
    workbox-broadcast-update: private
  workbox-build@6.6.0(@types/babel__core@7.20.5):
    workbox-build: private
  workbox-cacheable-response@6.6.0:
    workbox-cacheable-response: private
  workbox-core@6.6.0:
    workbox-core: private
  workbox-expiration@6.6.0:
    workbox-expiration: private
  workbox-google-analytics@6.6.0:
    workbox-google-analytics: private
  workbox-navigation-preload@6.6.0:
    workbox-navigation-preload: private
  workbox-precaching@6.6.0:
    workbox-precaching: private
  workbox-range-requests@6.6.0:
    workbox-range-requests: private
  workbox-recipes@6.6.0:
    workbox-recipes: private
  workbox-routing@6.6.0:
    workbox-routing: private
  workbox-strategies@6.6.0:
    workbox-strategies: private
  workbox-streams@6.6.0:
    workbox-streams: private
  workbox-sw@6.6.0:
    workbox-sw: private
  workbox-webpack-plugin@6.6.0(@types/babel__core@7.20.5)(webpack@5.100.2):
    workbox-webpack-plugin: private
  workbox-window@6.6.0:
    workbox-window: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@8.18.3:
    ws: private
  xml-name-validator@3.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@16.2.0:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.3
pendingBuilds: []
prunedAt: Wed, 23 Jul 2025 08:35:44 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\Downloads\singpass-demo-app-main\examples\react-frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 120
